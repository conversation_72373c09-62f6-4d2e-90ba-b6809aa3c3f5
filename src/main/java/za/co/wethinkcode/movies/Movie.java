package za.co.wethinkcode.movies;
import person.Person;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Objects;

public class Movie {
    private final String title;
    private String synopsis;
    private LocalDate releaseDate;
    private final Duration runTime;
    private Person director;

    public Movie(String title, String synopsis, LocalDate releaseDate, Person director, Duration runTime) {
        this.title = validateTitle(title);
        this.synopsis = synopsis == null ? "" : synopsis;
        this.releaseDate = Objects.requireNonNull(releaseDate);
        this.director = Objects.requireNonNull(director);
        this.runTime = Objects.requireNonNull(runTime);
    }

    private String validateTitle(String title) {
        if (title == null) {
            throw new NullPointerException("Title cannot be null");
        }
        if (title.trim().isEmpty()) {
            throw new IllegalArgumentException("Title cannot be empty");
        }
        return title.trim();
    }

    // Getters
    public String getTitle() { return title; }
    public String getSynopsis() { return synopsis; }
    public LocalDate getReleaseDate() { return releaseDate; }
    public Duration getRunTime() { return runTime; }
    public Person getDirector() { return director; }

    // Setters
    public void setSynopsis(String synopsis) {
        this.synopsis = synopsis == null ? "" : synopsis;
    }

    public void setReleaseDate(LocalDate releaseDate) {
        if (this.releaseDate.isBefore(LocalDate.now())) {
            // For published movies, don't change the release date
            return;
        }
        this.releaseDate = Objects.requireNonNull(releaseDate, "Release date cannot be null");
    }

    public void setDirector(Person director) {
        if (this.releaseDate.isBefore(LocalDate.now())) {
            // For published movies, don't change the director
            return;
        }
        this.director = Objects.requireNonNull(director, "Director cannot be null");
    }
}